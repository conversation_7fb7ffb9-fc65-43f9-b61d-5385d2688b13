import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:permission_handler/permission_handler.dart';
import '../utils/logger.dart';
import '../../features/notifications/domain/models/notification.dart';

/// Android-specific notification management and optimizations
class AndroidNotificationManager {
  static final AndroidNotificationManager _instance =
      AndroidNotificationManager._internal();

  factory AndroidNotificationManager() => _instance;
  AndroidNotificationManager._internal();

  final FlutterLocalNotificationsPlugin _notificationsPlugin =
      FlutterLocalNotificationsPlugin();

  /// Initialize Android-specific notification features
  Future<bool> initialize() async {
    if (!Platform.isAndroid) return true;

    try {
      Logger.info('AndroidNotificationManager: Initializing');

      // Create enhanced notification channels
      await _createEnhancedNotificationChannels();

      // Check and request additional permissions
      await _checkAndroidSpecificPermissions();

      Logger.info('AndroidNotificationManager: Initialization complete');
      return true;
    } catch (e) {
      Logger.error('AndroidNotificationManager: Initialization failed: $e');
      return false;
    }
  }

  /// Create enhanced notification channels with high priority settings
  Future<void> _createEnhancedNotificationChannels() async {
    try {
      final androidImplementation =
          _notificationsPlugin.resolvePlatformSpecificImplementation<
              AndroidFlutterLocalNotificationsPlugin>();

      if (androidImplementation == null) return;

      // Critical alerts channel with maximum priority
      const AndroidNotificationChannel criticalChannel =
          AndroidNotificationChannel(
        'lekky_critical_alerts_enhanced',
        'Critical Alerts (Enhanced)',
        description: 'High priority alerts for critical meter conditions',
        importance: Importance.max,
        enableVibration: true,
        enableLights: true,
        playSound: true,
        showBadge: true,
        ledColor: Color(0xFF4A90E2),
      );

      // Reminder channel with high priority
      const AndroidNotificationChannel reminderChannel =
          AndroidNotificationChannel(
        'lekky_reminders_enhanced',
        'Meter Reading Reminders (Enhanced)',
        description: 'Enhanced reminders for meter readings',
        importance: Importance.high,
        enableVibration: true,
        enableLights: true,
        playSound: true,
        showBadge: true,
        ledColor: Color(0xFF7E57C2),
      );

      // Threshold alerts channel
      const AndroidNotificationChannel thresholdChannel =
          AndroidNotificationChannel(
        'lekky_threshold_alerts_enhanced',
        'Threshold Alerts (Enhanced)',
        description: 'Enhanced alerts when approaching thresholds',
        importance: Importance.high,
        enableVibration: true,
        enableLights: true,
        playSound: true,
        showBadge: true,
        ledColor: Color(0xFFFF9999),
      );

      // Create all channels
      await androidImplementation.createNotificationChannel(criticalChannel);
      await androidImplementation.createNotificationChannel(reminderChannel);
      await androidImplementation.createNotificationChannel(thresholdChannel);

      Logger.info('Enhanced Android notification channels created');
    } catch (e) {
      Logger.error('Failed to create enhanced notification channels: $e');
    }
  }

  /// Check Android-specific permissions for enhanced notifications
  Future<void> _checkAndroidSpecificPermissions() async {
    try {
      // Check exact alarm permission (Android 12+)
      final exactAlarmStatus = await Permission.scheduleExactAlarm.status;
      Logger.info('Exact alarm permission: $exactAlarmStatus');

      // Check notification permission (Android 13+)
      final notificationStatus = await Permission.notification.status;
      Logger.info('Notification permission: $notificationStatus');

      // Check battery optimization status
      final batteryOptimizationStatus =
          await Permission.ignoreBatteryOptimizations.status;
      Logger.info('Battery optimization exemption: $batteryOptimizationStatus');

      // Log overall permission status
      final allGranted = exactAlarmStatus.isGranted &&
          notificationStatus.isGranted &&
          batteryOptimizationStatus.isGranted;

      Logger.info('All Android permissions granted: $allGranted');
    } catch (e) {
      Logger.error('Error checking Android-specific permissions: $e');
    }
  }

  /// Get enhanced Android notification details for specific notification type
  AndroidNotificationDetails getEnhancedNotificationDetails(
      NotificationType type) {
    switch (type) {
      case NotificationType.lowBalance:
        return const AndroidNotificationDetails(
          'lekky_critical_alerts_enhanced',
          'Critical Alerts (Enhanced)',
          channelDescription:
              'High priority alerts for critical meter conditions',
          importance: Importance.max,
          priority: Priority.max,
          icon: '@mipmap/ic_launcher',
          enableVibration: true,
          enableLights: true,
          playSound: true,
          autoCancel: false,
          ongoing: false,
          showWhen: true,
          when: null,
          usesChronometer: false,
          fullScreenIntent: true,
          category: AndroidNotificationCategory.alarm,
          visibility: NotificationVisibility.public,
          timeoutAfter: null,
          styleInformation: BigTextStyleInformation(''),
        );

      case NotificationType.readingReminder:
        return const AndroidNotificationDetails(
          'lekky_reminders_enhanced',
          'Meter Reading Reminders (Enhanced)',
          channelDescription: 'Enhanced reminders for meter readings',
          importance: Importance.high,
          priority: Priority.high,
          icon: '@mipmap/ic_launcher',
          enableVibration: true,
          enableLights: true,
          playSound: true,
          autoCancel: true,
          ongoing: false,
          showWhen: true,
          category: AndroidNotificationCategory.reminder,
          visibility: NotificationVisibility.public,
          styleInformation: BigTextStyleInformation(''),
        );

      case NotificationType.timeToTopUp:
      case NotificationType.invalidRecord:
        return const AndroidNotificationDetails(
          'lekky_threshold_alerts_enhanced',
          'Threshold Alerts (Enhanced)',
          channelDescription: 'Enhanced alerts when approaching thresholds',
          importance: Importance.high,
          priority: Priority.high,
          icon: '@mipmap/ic_launcher',
          enableVibration: true,
          enableLights: true,
          playSound: true,
          autoCancel: true,
          ongoing: false,
          showWhen: true,
          category: AndroidNotificationCategory.status,
          visibility: NotificationVisibility.public,
          styleInformation: BigTextStyleInformation(''),
        );

      case NotificationType.welcome:
      case NotificationType.appUpdate:
        return const AndroidNotificationDetails(
          'lekky_threshold_alerts_enhanced',
          'Threshold Alerts (Enhanced)',
          channelDescription: 'Enhanced alerts when approaching thresholds',
          importance: Importance.defaultImportance,
          priority: Priority.defaultPriority,
          icon: '@mipmap/ic_launcher',
          enableVibration: false,
          enableLights: false,
          playSound: true,
          autoCancel: true,
          ongoing: false,
          showWhen: true,
          category: AndroidNotificationCategory.status,
          visibility: NotificationVisibility.public,
        );
    }
  }

  /// Request all Android-specific permissions
  Future<bool> requestAllAndroidPermissions(BuildContext context) async {
    if (!Platform.isAndroid) return true;

    try {
      // Request notification permission
      final notificationGranted = await Permission.notification.request();

      // Request exact alarm permission
      final exactAlarmGranted = await Permission.scheduleExactAlarm.request();

      // Request battery optimization exemption
      final batteryOptimizationGranted =
          await Permission.ignoreBatteryOptimizations.request();

      final allGranted = notificationGranted.isGranted &&
          exactAlarmGranted.isGranted &&
          batteryOptimizationGranted.isGranted;

      Logger.info('Android permissions request result: $allGranted');
      return allGranted;
    } catch (e) {
      Logger.error('Error requesting Android permissions: $e');
      return false;
    }
  }

  /// Get Android notification system status
  Future<Map<String, dynamic>> getAndroidNotificationStatus() async {
    if (!Platform.isAndroid) return {'platform': 'not_android'};

    final status = <String, dynamic>{};

    try {
      status['platform'] = 'android';
      status['notificationPermission'] =
          (await Permission.notification.status).toString();
      status['exactAlarmPermission'] =
          (await Permission.scheduleExactAlarm.status).toString();
      status['batteryOptimizationExemption'] =
          (await Permission.ignoreBatteryOptimizations.status).toString();
      status['timestamp'] = DateTime.now().toIso8601String();
    } catch (e) {
      Logger.error('Error getting Android notification status: $e');
      status['error'] = e.toString();
    }

    return status;
  }
}
