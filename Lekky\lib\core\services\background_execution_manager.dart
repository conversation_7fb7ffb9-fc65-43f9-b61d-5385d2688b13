import 'dart:io';
import 'package:flutter/material.dart';
import '../utils/logger.dart';
import 'battery_optimization_manager.dart';

/// Manages background execution capabilities across platforms
class BackgroundExecutionManager {
  static final BackgroundExecutionManager _instance =
      BackgroundExecutionManager._internal();

  factory BackgroundExecutionManager() => _instance;
  BackgroundExecutionManager._internal();

  final BatteryOptimizationManager _batteryManager =
      BatteryOptimizationManager();

  /// Check if background execution is properly configured
  Future<bool> isBackgroundExecutionEnabled() async {
    try {
      if (Platform.isAndroid) {
        return await _checkAndroidBackgroundExecution();
      } else if (Platform.isIOS) {
        return await _checkIOSBackgroundExecution();
      }
      return false;
    } catch (e) {
      Logger.error('Error checking background execution: $e');
      return false;
    }
  }

  /// Check Android background execution capabilities
  Future<bool> _checkAndroidBackgroundExecution() async {
    try {
      // Check battery optimization status
      final isBatteryOptimized = await _batteryManager.isBatteryOptimized();

      // Check auto-start permission
      final hasAutoStart = await _batteryManager.hasAutoStartPermission();

      // Background execution is enabled if battery optimization is disabled
      // and auto-start is available
      return !isBatteryOptimized && hasAutoStart;
    } catch (e) {
      Logger.error('Error checking Android background execution: $e');
      return false;
    }
  }

  /// Check iOS background execution capabilities
  Future<bool> _checkIOSBackgroundExecution() async {
    try {
      // iOS background execution depends on background app refresh
      // This cannot be checked programmatically, so we assume it's enabled
      return true;
    } catch (e) {
      Logger.error('Error checking iOS background execution: $e');
      return false;
    }
  }

  /// Request background execution permissions
  Future<bool> requestBackgroundExecutionPermissions(
      BuildContext context) async {
    try {
      if (Platform.isAndroid) {
        return await _requestAndroidBackgroundPermissions(context);
      } else if (Platform.isIOS) {
        return await _requestIOSBackgroundPermissions(context);
      }
      return false;
    } catch (e) {
      Logger.error('Error requesting background execution permissions: $e');
      return false;
    }
  }

  /// Request Android background execution permissions
  Future<bool> _requestAndroidBackgroundPermissions(
      BuildContext context) async {
    try {
      // Request battery optimization exemption
      final batteryExemption =
          await _batteryManager.requestBatteryOptimizationExemption(context);

      if (batteryExemption) {
        // Show auto-start guidance
        if (context.mounted) {
          await _batteryManager.showAutoStartGuidance(context);
        }
        return true;
      }

      return false;
    } catch (e) {
      Logger.error('Error requesting Android background permissions: $e');
      return false;
    }
  }

  /// Request iOS background execution permissions
  Future<bool> _requestIOSBackgroundPermissions(BuildContext context) async {
    try {
      // Show background app refresh guidance
      await _showIOSBackgroundGuidance(context);
      return true;
    } catch (e) {
      Logger.error('Error requesting iOS background permissions: $e');
      return false;
    }
  }

  /// Show iOS background app refresh guidance
  Future<void> _showIOSBackgroundGuidance(BuildContext context) async {
    if (!context.mounted) return;
    await showDialog(
      context: context,
      builder: (BuildContext dialogContext) {
        return _buildIOSBackgroundDialog(dialogContext);
      },
    );
  }

  /// Build iOS background dialog with Lekky styling
  Widget _buildIOSBackgroundDialog(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final dialogWidth = screenWidth < 600 ? screenWidth * 0.95 : 500.0;
    final horizontalPadding = (screenWidth - dialogWidth) / 2;

    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      elevation: 24,
      insetPadding: EdgeInsets.symmetric(
        horizontal: horizontalPadding,
        vertical: 28,
      ),
      child: Container(
        constraints: BoxConstraints(
          maxHeight: screenHeight * 0.8,
        ),
        child: SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.all(24),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildIOSBackgroundHeader(context),
                const SizedBox(height: 16),
                _buildIOSBackgroundContent(context),
                const SizedBox(height: 24),
                _buildIOSBackgroundActions(context),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// Build iOS background dialog header
  Widget _buildIOSBackgroundHeader(BuildContext context) {
    return Row(
      children: [
        Icon(
          Icons.refresh,
          color: Theme.of(context).colorScheme.primary,
          size: 24,
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Text(
            'Background App Refresh',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
          ),
        ),
      ],
    );
  }

  /// Build iOS background dialog content
  Widget _buildIOSBackgroundContent(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'For reliable notifications when the app is closed, please ensure Background App Refresh is enabled for Lekky:',
          style: Theme.of(context).textTheme.bodyMedium,
        ),
        const SizedBox(height: 16),
        _buildNumberedStep(
            context, '1', 'Go to Settings > General > Background App Refresh'),
        _buildNumberedStep(
            context, '2', 'Make sure Background App Refresh is On'),
        _buildNumberedStep(
            context, '3', 'Find Lekky in the list and enable it'),
      ],
    );
  }

  /// Build numbered step
  Widget _buildNumberedStep(BuildContext context, String number, String text) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '$number. ',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
          ),
          Expanded(
            child: Text(
              text,
              style: Theme.of(context).textTheme.bodyMedium,
            ),
          ),
        ],
      ),
    );
  }

  /// Build iOS background dialog actions
  Widget _buildIOSBackgroundActions(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          style: TextButton.styleFrom(
            foregroundColor: Theme.of(context).colorScheme.primary,
            padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
          ),
          child: const Text('OK'),
        ),
      ],
    );
  }

  /// Get comprehensive background execution status
  Future<Map<String, dynamic>> getBackgroundExecutionStatus() async {
    final status = <String, dynamic>{};

    try {
      status['platform'] = Platform.operatingSystem;
      status['isBackgroundExecutionEnabled'] =
          await isBackgroundExecutionEnabled();

      if (Platform.isAndroid) {
        final batteryStatus =
            await _batteryManager.getBatteryOptimizationStatus();
        status['batteryOptimization'] = batteryStatus;
      }

      status['timestamp'] = DateTime.now().toIso8601String();
    } catch (e) {
      Logger.error('Error getting background execution status: $e');
      status['error'] = e.toString();
    }

    return status;
  }

  /// Show background execution troubleshooting guide
  Future<void> showTroubleshootingGuide(BuildContext context) async {
    if (!context.mounted) return;
    await showDialog(
      context: context,
      builder: (BuildContext dialogContext) {
        return _buildTroubleshootingDialog(dialogContext);
      },
    );
  }

  /// Build troubleshooting dialog with Lekky styling
  Widget _buildTroubleshootingDialog(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final dialogWidth = screenWidth < 600 ? screenWidth * 0.95 : 500.0;
    final horizontalPadding = (screenWidth - dialogWidth) / 2;

    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      elevation: 24,
      insetPadding: EdgeInsets.symmetric(
        horizontal: horizontalPadding,
        vertical: 28,
      ),
      child: Container(
        constraints: BoxConstraints(
          maxHeight: screenHeight * 0.8,
        ),
        child: SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.all(24),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildTroubleshootingHeader(context),
                const SizedBox(height: 16),
                _buildTroubleshootingContent(context),
                const SizedBox(height: 24),
                _buildTroubleshootingActions(context),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// Build troubleshooting dialog header
  Widget _buildTroubleshootingHeader(BuildContext context) {
    return Row(
      children: [
        Icon(
          Icons.help_outline,
          color: Theme.of(context).colorScheme.primary,
          size: 24,
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Text(
            'Notification Troubleshooting',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
          ),
        ),
      ],
    );
  }

  /// Build troubleshooting dialog content
  Widget _buildTroubleshootingContent(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'If notifications are not working when the app is closed:',
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
        ),
        const SizedBox(height: 16),
        if (Platform.isAndroid) ...[
          Text(
            'Android:',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
          ),
          const SizedBox(height: 8),
          _buildBulletPoint(context, 'Disable battery optimization for Lekky'),
          _buildBulletPoint(context, 'Enable auto-start permission'),
          _buildBulletPoint(context, 'Check Do Not Disturb settings'),
          _buildBulletPoint(
              context, 'Ensure notification permissions are granted'),
          const SizedBox(height: 16),
        ] else if (Platform.isIOS) ...[
          Text(
            'iOS:',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
          ),
          const SizedBox(height: 8),
          _buildBulletPoint(context, 'Enable Background App Refresh for Lekky'),
          _buildBulletPoint(context, 'Check notification permissions'),
          _buildBulletPoint(
              context, 'Ensure Do Not Disturb allows notifications'),
          const SizedBox(height: 16),
        ],
        Text(
          'General:',
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
        ),
        const SizedBox(height: 8),
        _buildBulletPoint(context, 'Check app notification settings'),
        _buildBulletPoint(context, 'Restart the app after changing settings'),
        _buildBulletPoint(context, 'Test with a short reminder interval'),
      ],
    );
  }

  /// Build bullet point text
  Widget _buildBulletPoint(BuildContext context, String text) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '• ',
            style: Theme.of(context).textTheme.bodyMedium,
          ),
          Expanded(
            child: Text(
              text,
              style: Theme.of(context).textTheme.bodyMedium,
            ),
          ),
        ],
      ),
    );
  }

  /// Build troubleshooting dialog actions
  Widget _buildTroubleshootingActions(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          style: TextButton.styleFrom(
            foregroundColor: Theme.of(context).colorScheme.primary,
            padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
          ),
          child: const Text('OK'),
        ),
      ],
    );
  }

  /// Initialize background execution manager
  Future<void> initialize(BuildContext context) async {
    try {
      Logger.info('BackgroundExecutionManager: Initializing');

      // Initialize battery optimization manager
      await _batteryManager.initialize(context);

      // Check current status
      final isEnabled = await isBackgroundExecutionEnabled();
      Logger.info('Background execution enabled: $isEnabled');

      Logger.info('BackgroundExecutionManager: Initialization complete');
    } catch (e) {
      Logger.error(
          'BackgroundExecutionManager: Error during initialization: $e');
    }
  }
}
