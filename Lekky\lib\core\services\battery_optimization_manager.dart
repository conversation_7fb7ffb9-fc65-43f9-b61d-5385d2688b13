import 'dart:io';
import 'package:flutter/material.dart';
import 'package:permission_handler/permission_handler.dart';
import '../utils/logger.dart';

/// Manages battery optimization settings to ensure background execution
class BatteryOptimizationManager {
  static final BatteryOptimizationManager _instance =
      BatteryOptimizationManager._internal();

  factory BatteryOptimizationManager() => _instance;
  BatteryOptimizationManager._internal();

  /// Check if the app is battery optimized (Android only)
  Future<bool> isBatteryOptimized() async {
    if (!Platform.isAndroid) return false;

    try {
      // Check if battery optimization is disabled for the app
      final status = await Permission.ignoreBatteryOptimizations.status;
      return !status.isGranted;
    } catch (e) {
      Logger.error('Error checking battery optimization status: $e');
      return true; // Assume optimized if we can't check
    }
  }

  /// Request battery optimization exemption
  Future<bool> requestBatteryOptimizationExemption(BuildContext context) async {
    if (!Platform.isAndroid) return true;

    try {
      // Show explanation dialog first
      final shouldRequest = await _showBatteryOptimizationDialog(context);
      if (!shouldRequest) return false;

      // Request permission to ignore battery optimizations
      final status = await Permission.ignoreBatteryOptimizations.request();
      
      if (status.isGranted) {
        Logger.info('Battery optimization exemption granted');
        return true;
      } else {
        Logger.warning('Battery optimization exemption denied');
        return false;
      }
    } catch (e) {
      Logger.error('Error requesting battery optimization exemption: $e');
      return false;
    }
  }

  /// Show battery optimization explanation dialog
  Future<bool> _showBatteryOptimizationDialog(BuildContext context) async {
    return await showDialog<bool>(
          context: context,
          barrierDismissible: false,
          builder: (BuildContext context) {
            return AlertDialog(
              title: const Text('Battery Optimization'),
              content: const Text(
                'To ensure reminders work when the app is closed, please disable battery optimization for Lekky. This allows the app to run background tasks for notifications.',
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(false),
                  child: const Text('Skip'),
                ),
                TextButton(
                  onPressed: () => Navigator.of(context).pop(true),
                  child: const Text('Allow'),
                ),
              ],
            );
          },
        ) ??
        false;
  }

  /// Check auto-start permission status (Android only)
  Future<bool> hasAutoStartPermission() async {
    if (!Platform.isAndroid) return true;

    try {
      // Note: Auto-start permissions are OEM-specific and cannot be checked programmatically
      // This is a placeholder for future implementation
      return true;
    } catch (e) {
      Logger.error('Error checking auto-start permission: $e');
      return false;
    }
  }

  /// Show auto-start permission guidance
  Future<void> showAutoStartGuidance(BuildContext context) async {
    if (!Platform.isAndroid) return;

    await showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Auto-Start Permission'),
          content: const Text(
            'For reliable notifications, please enable auto-start for Lekky in your device settings. This varies by manufacturer:\n\n'
            '• Xiaomi: Security > Permissions > Autostart\n'
            '• Huawei: Phone Manager > Protected Apps\n'
            '• OnePlus: Settings > Battery > Battery Optimization\n'
            '• Samsung: Settings > Device Care > Battery > App Power Management',
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('OK'),
            ),
          ],
        );
      },
    );
  }

  /// Get comprehensive battery optimization status
  Future<Map<String, dynamic>> getBatteryOptimizationStatus() async {
    final status = <String, dynamic>{};

    try {
      status['platform'] = Platform.operatingSystem;
      
      if (Platform.isAndroid) {
        status['isBatteryOptimized'] = await isBatteryOptimized();
        status['hasAutoStartPermission'] = await hasAutoStartPermission();
      } else {
        status['isBatteryOptimized'] = false;
        status['hasAutoStartPermission'] = true;
      }

      status['timestamp'] = DateTime.now().toIso8601String();
    } catch (e) {
      Logger.error('Error getting battery optimization status: $e');
      status['error'] = e.toString();
    }

    return status;
  }

  /// Initialize battery optimization handling
  Future<void> initialize(BuildContext context) async {
    try {
      Logger.info('BatteryOptimizationManager: Initializing');

      if (Platform.isAndroid) {
        final isOptimized = await isBatteryOptimized();
        
        if (isOptimized) {
          Logger.info('App is battery optimized - may affect background notifications');
          // Could show a non-blocking notification about this
        } else {
          Logger.info('App has battery optimization exemption');
        }
      }

      Logger.info('BatteryOptimizationManager: Initialization complete');
    } catch (e) {
      Logger.error('BatteryOptimizationManager: Error during initialization: $e');
    }
  }
}
