import 'dart:math';
import 'package:shared_preferences/shared_preferences.dart';
import '../utils/logger.dart';
import '../../features/notifications/domain/models/notification.dart';

/// Intelligent notification scheduler that learns user patterns
class IntelligentNotificationScheduler {
  static final IntelligentNotificationScheduler _instance =
      IntelligentNotificationScheduler._internal();

  factory IntelligentNotificationScheduler() => _instance;
  IntelligentNotificationScheduler._internal();

  static const String _userPatternsKey = 'user_notification_patterns';
  static const String _notificationHistoryKey =
      'notification_interaction_history';
  static const int _maxHistoryEntries = 100;

  /// Analyze user patterns and suggest optimal notification timing
  Future<DateTime> getOptimalNotificationTime(
    DateTime baseTime,
    NotificationType type,
  ) async {
    try {
      final patterns = await _getUserPatterns();
      final optimalHour = await _getOptimalHourForType(type, patterns);

      // Adjust base time to optimal hour
      final optimizedTime = DateTime(
        baseTime.year,
        baseTime.month,
        baseTime.day,
        optimalHour,
        baseTime.minute,
      );

      // Ensure the time is in the future
      if (optimizedTime.isBefore(DateTime.now())) {
        return optimizedTime.add(const Duration(days: 1));
      }

      Logger.info(
          'Optimized notification time from ${baseTime.hour}:${baseTime.minute} to ${optimizedTime.hour}:${optimizedTime.minute}');

      return optimizedTime;
    } catch (e) {
      Logger.error('Error optimizing notification time: $e');
      return baseTime; // Fallback to original time
    }
  }

  /// Record user interaction with notification
  Future<void> recordNotificationInteraction({
    required NotificationType type,
    required DateTime notificationTime,
    required bool wasInteracted,
    required Duration responseTime,
  }) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final historyJson = prefs.getStringList(_notificationHistoryKey) ?? [];

      final interaction = {
        'type': type.index,
        'notificationTime': notificationTime.toIso8601String(),
        'wasInteracted': wasInteracted,
        'responseTimeMinutes': responseTime.inMinutes,
        'recordedAt': DateTime.now().toIso8601String(),
      };

      historyJson.add(_encodeInteraction(interaction));

      // Keep only recent entries
      if (historyJson.length > _maxHistoryEntries) {
        historyJson.removeRange(0, historyJson.length - _maxHistoryEntries);
      }

      await prefs.setStringList(_notificationHistoryKey, historyJson);

      // Update user patterns
      await _updateUserPatterns(interaction);

      Logger.info('Recorded notification interaction: $interaction');
    } catch (e) {
      Logger.error('Error recording notification interaction: $e');
    }
  }

  /// Get user engagement patterns for different notification types
  Future<Map<NotificationType, double>> getEngagementRates() async {
    try {
      final history = await _getNotificationHistory();
      final engagementRates = <NotificationType, double>{};

      for (final type in NotificationType.values) {
        final typeInteractions = history.where((h) => h['type'] == type.index);
        if (typeInteractions.isNotEmpty) {
          final interactedCount =
              typeInteractions.where((h) => h['wasInteracted'] == true).length;
          engagementRates[type] = interactedCount / typeInteractions.length;
        } else {
          engagementRates[type] = 0.0;
        }
      }

      return engagementRates;
    } catch (e) {
      Logger.error('Error calculating engagement rates: $e');
      return {};
    }
  }

  /// Suggest optimal reminder frequency based on user behavior
  Future<String> suggestOptimalFrequency() async {
    try {
      final patterns = await _getUserPatterns();
      final avgResponseTime =
          patterns['avgResponseTimeHours'] as double? ?? 24.0;
      final engagementRate =
          patterns['overallEngagementRate'] as double? ?? 0.5;

      // Logic for frequency suggestion
      if (engagementRate > 0.8 && avgResponseTime < 2.0) {
        return 'daily'; // High engagement, quick response
      } else if (engagementRate > 0.6 && avgResponseTime < 12.0) {
        return 'bi-weekly'; // Good engagement, moderate response
      } else if (engagementRate > 0.4) {
        return 'weekly'; // Moderate engagement
      } else {
        return 'monthly'; // Low engagement, less frequent
      }
    } catch (e) {
      Logger.error('Error suggesting optimal frequency: $e');
      return 'weekly'; // Default fallback
    }
  }

  /// Get personalized notification content based on user patterns
  Future<Map<String, String>> getPersonalizedContent(
    NotificationType type,
    Map<String, dynamic> context,
  ) async {
    try {
      final patterns = await _getUserPatterns();
      final preferredTone = patterns['preferredTone'] as String? ?? 'friendly';

      return _generateContentForTone(type, preferredTone, context);
    } catch (e) {
      Logger.error('Error generating personalized content: $e');
      return _getDefaultContent(type);
    }
  }

  /// Predict best time for next notification based on historical data
  Future<DateTime> predictOptimalTime(NotificationType type) async {
    try {
      final history = await _getNotificationHistory();
      final typeHistory = history
          .where((h) => h['type'] == type.index && h['wasInteracted'] == true)
          .toList();

      if (typeHistory.isEmpty) {
        // No history, use default time (9 AM)
        final now = DateTime.now();
        return DateTime(now.year, now.month, now.day, 9, 0);
      }

      // Calculate average interaction time
      final interactionHours = typeHistory.map((h) {
        final notificationTime =
            DateTime.parse(h['notificationTime'] as String);
        return notificationTime.hour + (notificationTime.minute / 60.0);
      }).toList();

      final avgHour =
          interactionHours.reduce((a, b) => a + b) / interactionHours.length;
      final hour = avgHour.round();
      final minute = ((avgHour - hour) * 60).round();

      final now = DateTime.now();
      var predictedTime = DateTime(now.year, now.month, now.day, hour, minute);

      // Ensure future time
      if (predictedTime.isBefore(now)) {
        predictedTime = predictedTime.add(const Duration(days: 1));
      }

      Logger.info(
          'Predicted optimal time for $type: ${predictedTime.hour}:${predictedTime.minute}');
      return predictedTime;
    } catch (e) {
      Logger.error('Error predicting optimal time: $e');
      final now = DateTime.now();
      return DateTime(now.year, now.month, now.day, 9, 0);
    }
  }

  /// Get user patterns from storage
  Future<Map<String, dynamic>> _getUserPatterns() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final patternsJson = prefs.getString(_userPatternsKey);

      if (patternsJson != null) {
        return _decodePatterns(patternsJson);
      }

      return _getDefaultPatterns();
    } catch (e) {
      Logger.error('Error loading user patterns: $e');
      return _getDefaultPatterns();
    }
  }

  /// Update user patterns based on new interaction
  Future<void> _updateUserPatterns(Map<String, dynamic> interaction) async {
    try {
      final patterns = await _getUserPatterns();
      final notificationTime =
          DateTime.parse(interaction['notificationTime'] as String);

      // Update preferred hours
      final hour = notificationTime.hour;
      final hourCounts =
          Map<String, int>.from(patterns['hourCounts'] as Map? ?? {});
      hourCounts[hour.toString()] = (hourCounts[hour.toString()] ?? 0) + 1;
      patterns['hourCounts'] = hourCounts;

      // Update engagement rate
      final totalInteractions =
          (patterns['totalInteractions'] as int? ?? 0) + 1;
      final successfulInteractions =
          (patterns['successfulInteractions'] as int? ?? 0) +
              (interaction['wasInteracted'] as bool ? 1 : 0);

      patterns['totalInteractions'] = totalInteractions;
      patterns['successfulInteractions'] = successfulInteractions;
      patterns['overallEngagementRate'] =
          successfulInteractions / totalInteractions;

      // Update average response time
      if (interaction['wasInteracted'] as bool) {
        final responseTimeHours =
            (interaction['responseTimeMinutes'] as int) / 60.0;
        final currentAvg =
            patterns['avgResponseTimeHours'] as double? ?? responseTimeHours;
        final responseCount = (patterns['responseCount'] as int? ?? 0) + 1;

        patterns['avgResponseTimeHours'] =
            ((currentAvg * (responseCount - 1)) + responseTimeHours) /
                responseCount;
        patterns['responseCount'] = responseCount;
      }

      // Save updated patterns
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_userPatternsKey, _encodePatterns(patterns));
    } catch (e) {
      Logger.error('Error updating user patterns: $e');
    }
  }

  /// Get optimal hour for notification type based on patterns
  Future<int> _getOptimalHourForType(
    NotificationType type,
    Map<String, dynamic> patterns,
  ) async {
    try {
      final hourCounts =
          Map<String, int>.from(patterns['hourCounts'] as Map? ?? {});

      if (hourCounts.isEmpty) {
        // Default times based on notification type
        switch (type) {
          case NotificationType.readingReminder:
            return 9; // 9 AM for reminders
          case NotificationType.lowBalance:
          case NotificationType.timeToTopUp:
            return 10; // 10 AM for alerts
          default:
            return 12; // Noon for other notifications
        }
      }

      // Find hour with highest interaction count
      final sortedHours = hourCounts.entries.toList()
        ..sort((a, b) => b.value.compareTo(a.value));

      return int.parse(sortedHours.first.key);
    } catch (e) {
      Logger.error('Error getting optimal hour: $e');
      return 9; // Default fallback
    }
  }

  /// Get notification interaction history
  Future<List<Map<String, dynamic>>> _getNotificationHistory() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final historyJson = prefs.getStringList(_notificationHistoryKey) ?? [];

      return historyJson.map((json) => _decodeInteraction(json)).toList();
    } catch (e) {
      Logger.error('Error loading notification history: $e');
      return [];
    }
  }

  /// Generate content based on preferred tone
  Map<String, String> _generateContentForTone(
    NotificationType type,
    String tone,
    Map<String, dynamic> context,
  ) {
    switch (tone) {
      case 'formal':
        return _getFormalContent(type, context);
      case 'casual':
        return _getCasualContent(type, context);
      case 'urgent':
        return _getUrgentContent(type, context);
      default:
        return _getFriendlyContent(type, context);
    }
  }

  Map<String, String> _getFriendlyContent(
      NotificationType type, Map<String, dynamic> context) {
    switch (type) {
      case NotificationType.readingReminder:
        return {
          'title': 'Time for a meter check! 📊',
          'message':
              'Hey! Just a friendly reminder to check your electricity meter.',
        };
      case NotificationType.lowBalance:
        return {
          'title': 'Low balance heads up! ⚡',
          'message': 'Your meter balance is getting low. Time to top up soon!',
        };
      default:
        return _getDefaultContent(type);
    }
  }

  Map<String, String> _getFormalContent(
      NotificationType type, Map<String, dynamic> context) {
    switch (type) {
      case NotificationType.readingReminder:
        return {
          'title': 'Meter Reading Reminder',
          'message': 'Please take your scheduled electricity meter reading.',
        };
      case NotificationType.lowBalance:
        return {
          'title': 'Low Balance Alert',
          'message': 'Your electricity meter balance requires attention.',
        };
      default:
        return _getDefaultContent(type);
    }
  }

  Map<String, String> _getCasualContent(
      NotificationType type, Map<String, dynamic> context) {
    switch (type) {
      case NotificationType.readingReminder:
        return {
          'title': 'Meter check time! 👀',
          'message': 'Quick meter reading needed - won\'t take a sec!',
        };
      case NotificationType.lowBalance:
        return {
          'title': 'Running low! 🔋',
          'message': 'Better top up soon before the lights go out!',
        };
      default:
        return _getDefaultContent(type);
    }
  }

  Map<String, String> _getUrgentContent(
      NotificationType type, Map<String, dynamic> context) {
    switch (type) {
      case NotificationType.readingReminder:
        return {
          'title': 'URGENT: Meter Reading Required',
          'message':
              'Important: Take your meter reading now to avoid service issues.',
        };
      case NotificationType.lowBalance:
        return {
          'title': 'CRITICAL: Low Balance',
          'message':
              'URGENT: Your meter balance is critically low. Top up immediately!',
        };
      default:
        return _getDefaultContent(type);
    }
  }

  Map<String, String> _getDefaultContent(NotificationType type) {
    switch (type) {
      case NotificationType.readingReminder:
        return {
          'title': 'Meter Reading Reminder',
          'message': 'It\'s time to take a new meter reading.',
        };
      case NotificationType.lowBalance:
        return {
          'title': 'Low Balance Alert',
          'message': 'Your meter balance is low.',
        };
      default:
        return {
          'title': 'Lekky Notification',
          'message': 'You have a new notification.',
        };
    }
  }

  Map<String, dynamic> _getDefaultPatterns() {
    return {
      'hourCounts': <String, int>{},
      'totalInteractions': 0,
      'successfulInteractions': 0,
      'overallEngagementRate': 0.0,
      'avgResponseTimeHours': 24.0,
      'responseCount': 0,
      'preferredTone': 'friendly',
    };
  }

  String _encodeInteraction(Map<String, dynamic> interaction) {
    // Simple encoding - in production, use proper JSON encoding
    return interaction.entries.map((e) => '${e.key}:${e.value}').join('|');
  }

  Map<String, dynamic> _decodeInteraction(String encoded) {
    // Simple decoding - in production, use proper JSON decoding
    final parts = encoded.split('|');
    final interaction = <String, dynamic>{};

    for (final part in parts) {
      final keyValue = part.split(':');
      if (keyValue.length == 2) {
        final key = keyValue[0];
        final value = keyValue[1];

        // Parse values based on key
        switch (key) {
          case 'type':
          case 'responseTimeMinutes':
            interaction[key] = int.tryParse(value) ?? 0;
            break;
          case 'wasInteracted':
            interaction[key] = value == 'true';
            break;
          default:
            interaction[key] = value;
        }
      }
    }

    return interaction;
  }

  String _encodePatterns(Map<String, dynamic> patterns) {
    // Simple encoding - in production, use proper JSON encoding
    return patterns.entries.map((e) => '${e.key}:${e.value}').join('|');
  }

  Map<String, dynamic> _decodePatterns(String encoded) {
    // Simple decoding - in production, use proper JSON decoding
    final parts = encoded.split('|');
    final patterns = <String, dynamic>{};

    for (final part in parts) {
      final keyValue = part.split(':');
      if (keyValue.length == 2) {
        final key = keyValue[0];
        final value = keyValue[1];

        // Parse values based on key
        switch (key) {
          case 'totalInteractions':
          case 'successfulInteractions':
          case 'responseCount':
            patterns[key] = int.tryParse(value) ?? 0;
            break;
          case 'overallEngagementRate':
          case 'avgResponseTimeHours':
            patterns[key] = double.tryParse(value) ?? 0.0;
            break;
          case 'hourCounts':
            // This would need more complex parsing in production
            patterns[key] = <String, int>{};
            break;
          default:
            patterns[key] = value;
        }
      }
    }

    return patterns;
  }

  /// Clear all user patterns and history (for testing or reset)
  Future<void> clearAllData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_userPatternsKey);
      await prefs.remove(_notificationHistoryKey);
      Logger.info('Cleared all intelligent notification data');
    } catch (e) {
      Logger.error('Error clearing intelligent notification data: $e');
    }
  }

  /// Get analytics summary for debugging/admin purposes
  Future<Map<String, dynamic>> getAnalyticsSummary() async {
    try {
      final patterns = await _getUserPatterns();
      final history = await _getNotificationHistory();
      final engagementRates = await getEngagementRates();

      return {
        'totalInteractions': patterns['totalInteractions'] ?? 0,
        'overallEngagementRate': patterns['overallEngagementRate'] ?? 0.0,
        'avgResponseTimeHours': patterns['avgResponseTimeHours'] ?? 0.0,
        'historyEntries': history.length,
        'engagementByType':
            engagementRates.map((k, v) => MapEntry(k.toString(), v)),
        'suggestedFrequency': await suggestOptimalFrequency(),
        'lastUpdated': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      Logger.error('Error generating analytics summary: $e');
      return {'error': e.toString()};
    }
  }
}
