import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import '../utils/logger.dart';
import '../../features/notifications/domain/models/notification.dart';

/// iOS-specific notification management and optimizations
class IOSNotificationManager {
  static final IOSNotificationManager _instance =
      IOSNotificationManager._internal();

  factory IOSNotificationManager() => _instance;
  IOSNotificationManager._internal();

  final FlutterLocalNotificationsPlugin _notificationsPlugin =
      FlutterLocalNotificationsPlugin();

  /// Initialize iOS-specific notification features
  Future<bool> initialize() async {
    if (!Platform.isIOS) return true;

    try {
      Logger.info('IOSNotificationManager: Initializing');

      // Request enhanced iOS permissions
      await _requestEnhancedIOSPermissions();

      // Check background app refresh status
      await _checkBackgroundAppRefreshStatus();

      Logger.info('IOSNotificationManager: Initialization complete');
      return true;
    } catch (e) {
      Logger.error('IOSNotificationManager: Initialization failed: $e');
      return false;
    }
  }

  /// Request enhanced iOS notification permissions
  Future<void> _requestEnhancedIOSPermissions() async {
    try {
      final iosImplementation =
          _notificationsPlugin.resolvePlatformSpecificImplementation<
              IOSFlutterLocalNotificationsPlugin>();

      if (iosImplementation == null) return;

      // Request comprehensive permissions
      final granted = await iosImplementation.requestPermissions(
        alert: true,
        badge: true,
        sound: true,
        critical: true, // For critical alerts
        provisional: false, // Require explicit permission
      );

      Logger.info('iOS notification permissions granted: $granted');
    } catch (e) {
      Logger.error('Error requesting iOS permissions: $e');
    }
  }

  /// Check background app refresh status
  Future<void> _checkBackgroundAppRefreshStatus() async {
    try {
      // Background app refresh status cannot be checked programmatically
      // This is a placeholder for logging purposes
      Logger.info('Background app refresh status check completed');
    } catch (e) {
      Logger.error('Error checking background app refresh: $e');
    }
  }

  /// Get enhanced iOS notification details for specific notification type
  DarwinNotificationDetails getEnhancedNotificationDetails(
      NotificationType type) {
    switch (type) {
      case NotificationType.lowBalance:
        return const DarwinNotificationDetails(
          presentAlert: true,
          presentBadge: true,
          presentSound: true,
          sound: 'default',
          badgeNumber: null,
          attachments: null,
          subtitle: null,
          threadIdentifier: 'lekky_critical',
          presentBanner: true,
          presentList: true,
          interruptionLevel: InterruptionLevel.critical,
          categoryIdentifier: 'CRITICAL_ALERT',
        );

      case NotificationType.readingReminder:
        return const DarwinNotificationDetails(
          presentAlert: true,
          presentBadge: true,
          presentSound: true,
          sound: 'default',
          badgeNumber: null,
          attachments: null,
          subtitle: null,
          threadIdentifier: 'lekky_reminders',
          presentBanner: true,
          presentList: true,
          interruptionLevel: InterruptionLevel.timeSensitive,
          categoryIdentifier: 'REMINDER_ALERT',
        );

      case NotificationType.timeToTopUp:
      case NotificationType.invalidRecord:
        return const DarwinNotificationDetails(
          presentAlert: true,
          presentBadge: true,
          presentSound: true,
          sound: 'default',
          badgeNumber: null,
          attachments: null,
          subtitle: null,
          threadIdentifier: 'lekky_alerts',
          presentBanner: true,
          presentList: true,
          interruptionLevel: InterruptionLevel.active,
          categoryIdentifier: 'ALERT',
        );

      case NotificationType.welcome:
      case NotificationType.appUpdate:
        return const DarwinNotificationDetails(
          presentAlert: true,
          presentBadge: true,
          presentSound: true,
          sound: 'default',
          badgeNumber: null,
          attachments: null,
          subtitle: null,
          threadIdentifier: 'lekky_info',
          presentBanner: true,
          presentList: true,
          interruptionLevel: InterruptionLevel.passive,
          categoryIdentifier: 'INFO',
        );
    }
  }

  /// Show iOS background app refresh guidance
  Future<void> showBackgroundAppRefreshGuidance(BuildContext context) async {
    await showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Background App Refresh'),
          content: const Text(
            'For reliable notifications when the app is closed, please ensure Background App Refresh is enabled for Lekky:\n\n'
            '1. Go to Settings > General > Background App Refresh\n'
            '2. Make sure Background App Refresh is On\n'
            '3. Find Lekky in the list and enable it\n\n'
            'This allows the app to refresh content and schedule notifications in the background.',
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('OK'),
            ),
          ],
        );
      },
    );
  }

  /// Show iOS notification troubleshooting guide
  Future<void> showIOSTroubleshootingGuide(BuildContext context) async {
    await showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('iOS Notification Troubleshooting'),
          content: const SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  'If notifications are not working:',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                SizedBox(height: 12),
                Text('1. Check notification permissions:'),
                Text('   Settings > Lekky > Notifications'),
                SizedBox(height: 8),
                Text('2. Enable Background App Refresh:'),
                Text('   Settings > General > Background App Refresh'),
                SizedBox(height: 8),
                Text('3. Check Do Not Disturb settings:'),
                Text('   Control Center > Focus'),
                SizedBox(height: 8),
                Text('4. Ensure critical alerts are enabled:'),
                Text('   Settings > Lekky > Notifications > Critical Alerts'),
                SizedBox(height: 8),
                Text('5. Restart the app after changing settings'),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('OK'),
            ),
          ],
        );
      },
    );
  }

  /// Request critical alert permission (iOS 12+)
  Future<bool> requestCriticalAlertPermission() async {
    if (!Platform.isIOS) return true;

    try {
      final iosImplementation =
          _notificationsPlugin.resolvePlatformSpecificImplementation<
              IOSFlutterLocalNotificationsPlugin>();

      if (iosImplementation == null) return false;

      final granted = await iosImplementation.requestPermissions(
        alert: true,
        badge: true,
        sound: true,
        critical: true,
      );

      Logger.info('iOS critical alert permission granted: $granted');
      return granted ?? false;
    } catch (e) {
      Logger.error('Error requesting iOS critical alert permission: $e');
      return false;
    }
  }

  /// Get iOS notification system status
  Future<Map<String, dynamic>> getIOSNotificationStatus() async {
    if (!Platform.isIOS) return {'platform': 'not_ios'};

    final status = <String, dynamic>{};

    try {
      status['platform'] = 'ios';

      final iosImplementation =
          _notificationsPlugin.resolvePlatformSpecificImplementation<
              IOSFlutterLocalNotificationsPlugin>();

      if (iosImplementation != null) {
        // Check current permission status
        final permissionsGranted = await iosImplementation.requestPermissions(
          alert: false, // Don't request, just check
          badge: false,
          sound: false,
          critical: false,
        );

        status['permissionsGranted'] = permissionsGranted;
      }

      status['timestamp'] = DateTime.now().toIso8601String();
    } catch (e) {
      Logger.error('Error getting iOS notification status: $e');
      status['error'] = e.toString();
    }

    return status;
  }

  /// Configure iOS notification categories for interactive notifications
  Future<void> configureNotificationCategories() async {
    if (!Platform.isIOS) return;

    try {
      final iosImplementation =
          _notificationsPlugin.resolvePlatformSpecificImplementation<
              IOSFlutterLocalNotificationsPlugin>();

      if (iosImplementation == null) return;

      // Define notification categories with actions
      // Note: DarwinNotificationAction and DarwinNotificationCategory are not available
      // in the current flutter_local_notifications version. This is a placeholder for
      // future implementation when these features become available.

      Logger.info('iOS notification categories would be configured here');
      // This functionality will be implemented when the required classes are available

      await iosImplementation.initialize(
        const DarwinInitializationSettings(
          requestAlertPermission: false,
          requestBadgePermission: false,
          requestSoundPermission: false,
          requestCriticalPermission: false,
        ),
      );

      Logger.info('iOS notification categories configured');
    } catch (e) {
      Logger.error('Error configuring iOS notification categories: $e');
    }
  }
}
