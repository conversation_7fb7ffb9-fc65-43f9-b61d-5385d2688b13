// File: lib/features/cost/data/cost_repository.dart
import '../../../core/utils/logger.dart';
import '../../../core/utils/entry_filter_utils.dart';

import '../../../core/di/service_locator.dart';
import '../../averages/domain/services/average_service.dart';
import '../../averages/domain/repositories/per_reading_average_repository.dart';
import '../domain/services/hybrid_cost_service.dart';
import '../../meter_readings/domain/repositories/meter_reading_repository.dart';
import '../../top_ups/domain/repositories/top_up_repository.dart';
import '../domain/models/cost_period.dart';
import '../domain/models/cost_result.dart';
import '../presentation/models/chart_data.dart';
import '../../../core/constants/preference_keys.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// Repository for cost-related operations
class CostRepository {
  final MeterReadingRepository _meterReadingRepository;
  final TopUpRepository _topUpRepository;

  final PerReadingAverageRepository _perReadingAverageRepository;
  final HybridCostService _hybridCostService;

  /// Constructor
  CostRepository({
    MeterReadingRepository? meterReadingRepository,
    TopUpRepository? topUpRepository,
    PerReadingAverageRepository? perReadingAverageRepository,
    HybridCostService? hybridCostService,
  })  : _meterReadingRepository =
            meterReadingRepository ?? serviceLocator<MeterReadingRepository>(),
        _topUpRepository = topUpRepository ?? serviceLocator<TopUpRepository>(),
        _perReadingAverageRepository = perReadingAverageRepository ??
            serviceLocator<PerReadingAverageRepository>(),
        _hybridCostService =
            hybridCostService ?? serviceLocator<HybridCostService>();

  /// Calculate historic cost using hybrid approach with detailed method tracking
  Future<Map<String, dynamic>> calculateHistoricCostUsingStoredAverages(
      DateTime fromDate, DateTime toDate) async {
    Logger.info(
        'CostRepository: Using historic cost calculation for period ${fromDate.toIso8601String()} to ${toDate.toIso8601String()}');

    // Check for gaps in records first
    final hasGap = await _checkForGapsInPeriod(fromDate, toDate);
    if (hasGap) {
      Logger.info('CostRepository: Gap detected in records for the period');
      return {
        'cost': 0.0,
        'method': CalculationMethod.gapInRecords,
      };
    }

    // Get detailed calculation result from hybrid service
    final result = await _hybridCostService.calculateCustomPeriodCostWithMethod(
        fromDate, toDate);

    Logger.info(
        'CostRepository: Historic calculation completed - Cost: ${result['cost']}, Method: ${result['method']}');

    return result;
  }

  /// Check if there are gaps in records for the given period
  Future<bool> _checkForGapsInPeriod(DateTime fromDate, DateTime toDate) async {
    try {
      final allReadings = await _meterReadingRepository.getAllMeterReadings();
      if (allReadings.isEmpty) return false;

      // Filter out record gap entries for gap detection
      final realReadings = allReadings
          .where((reading) => reading.notes?.contains('Records Gap:') != true)
          .toList();

      realReadings.sort((a, b) => a.date.compareTo(b.date));

      // Check if the requested period falls within a gap > 62 days
      for (int i = 1; i < realReadings.length; i++) {
        final previous = realReadings[i - 1].date;
        final current = realReadings[i].date;
        final gapDays = current.difference(previous).inDays;

        if (gapDays > 62) {
          // Check if the requested period overlaps with this gap
          final gapStart = previous;
          final gapEnd = current;

          // Period overlaps with gap if it starts before gap ends and ends after gap starts
          if (fromDate.isBefore(gapEnd) && toDate.isAfter(gapStart)) {
            return true;
          }
        }
      }

      return false;
    } catch (e) {
      Logger.error('CostRepository: Error checking for gaps: $e');
      return false;
    }
  }

  /// Fallback calculation using raw meter readings
  Future<double> calculateUsingRawMeterReadings(
      DateTime fromDate, DateTime toDate) async {
    try {
      final averageService = serviceLocator<AverageService>();
      final result = await averageService.getAverages();
      final days = toDate.difference(fromDate).inDays; // Remove +1
      return (result.totalAverage ?? 0.0) * days;
    } catch (e) {
      Logger.error('Failed to calculate using raw meter readings: $e');
      return 0.0;
    }
  }

  /// Calculate the cost for a specific period
  Future<CostResult> calculateCostForPeriod(
      CostPeriod period, DateTime? fromDate, DateTime? toDate) async {
    try {
      final meterReadings = await _meterReadingRepository.getAllMeterReadings();
      final topUps = await _topUpRepository.getAllTopUps();

      Logger.info(
          'CostRepository: Found ${meterReadings.length} meter readings and ${topUps.length} top-ups');

      double averageUsage = 0.0;
      double? topUpAmount;
      DateTime? topUpDate;

      final meterUnit = await getMeterUnit();

      double costPerPeriod;
      CalculationMethod calculationMethod;

      final effectiveFromDate =
          fromDate ?? DateTime.now().subtract(Duration(days: period.days));
      final effectiveToDate = toDate ?? DateTime.now();

      try {
        final averageService = serviceLocator<AverageService>();
        final result = await averageService.getAverages();
        averageUsage = result.totalAverage ?? 0.0;
      } catch (e) {
        if (meterReadings.length >= 2) {
          final sortedReadings = List.from(meterReadings)
            ..sort((a, b) => a.date.compareTo(b.date));
          final firstReading = sortedReadings.first;
          final lastReading = sortedReadings.last;
          final totalDays =
              lastReading.date.difference(firstReading.date).inDays;

          if (totalDays > 0) {
            double totalTopUps = 0;
            for (var topUp in topUps) {
              if (topUp.date.isAfter(firstReading.date) &&
                  topUp.date.isBefore(lastReading.date)) {
                totalTopUps += topUp.amount;
              }
            }
            final totalUsage =
                firstReading.value - lastReading.value + totalTopUps;
            if (totalUsage > 0) {
              averageUsage = totalUsage / totalDays;
            }
          }
        }
      }

      int actualDays;
      if (period == CostPeriod.custom && fromDate != null && toDate != null) {
        final costResult =
            await calculateHistoricCostUsingStoredAverages(fromDate, toDate);
        costPerPeriod = costResult['cost'] as double;
        calculationMethod = costResult['method'] as CalculationMethod;
        actualDays =
            period.calculateActualDays(fromDate: fromDate, toDate: toDate);
        averageUsage = actualDays > 0 ? costPerPeriod / actualDays : 0.0;
        Logger.info(
            'CostRepository: Custom period calculation - Method: $calculationMethod, Cost: $costPerPeriod, Days: $actualDays');
      } else {
        // Use calendar-aware calculation for month/year periods
        actualDays =
            period.calculateActualDays(fromDate: fromDate, toDate: toDate);
        costPerPeriod = averageUsage * actualDays;
        calculationMethod = CalculationMethod.totalAverageProjection;
      }
      Logger.info(
          'CostRepository: Final cost calculation - Average usage: $averageUsage, Actual days: $actualDays, Cost per period: $costPerPeriod');

      if (topUps.isNotEmpty) {
        final latestTopUp = topUps.first; // Already sorted by date DESC
        topUpAmount = latestTopUp.amount;
        topUpDate = latestTopUp.date;
      }

      final dailyRate = averageUsage; // Daily rate is the average usage per day

      Logger.info(
          'CostRepository: Creating CostResult with calculationMethod: $calculationMethod');

      return CostResult(
        averageUsage: averageUsage,
        costPerPeriod: costPerPeriod,
        period: period,
        meterUnit: meterUnit,
        actualDays: actualDays,
        dailyRate: dailyRate,
        calculationMethod: calculationMethod,
        topUpAmount: topUpAmount,
        topUpDate: topUpDate,
        fromDate: effectiveFromDate,
        toDate: effectiveToDate,
      );
    } catch (e) {
      Logger.error('Failed to calculate cost for period: $e');
      return CostResult(
        averageUsage: 0.0,
        costPerPeriod: 0.0,
        period: period,
        meterUnit: await getMeterUnit(),
        actualDays: 0,
        dailyRate: 0.0,
        calculationMethod: CalculationMethod.historicTotalAverage,
        fromDate: fromDate,
        toDate: toDate,
      );
    }
  }

  /// Get the meter unit from user preferences
  Future<String> getMeterUnit() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getString(PreferenceKeys.currencySymbol) ?? '₦';
    } catch (e) {
      Logger.error('Failed to get currency symbol: $e');
      return '₦'; // Default fallback
    }
  }

  /// Get the total average usage using AverageService with fallback
  Future<double> getTotalAverageUsage() async {
    try {
      final averageService = serviceLocator<AverageService>();
      final result = await averageService.getAverages();
      Logger.info(
          'CostRepository: Got total average from database: ${result.totalAverage}');
      return result.totalAverage ?? 0.0;
    } catch (e) {
      Logger.error('CostRepository: Failed to get averages from service: $e');
      return await _calculateTotalAverageFallback();
    }
  }

  /// Fallback method for calculating total average
  Future<double> _calculateTotalAverageFallback() async {
    try {
      Logger.info('CostRepository: Using fallback calculation');

      final meterReadings = await _meterReadingRepository.getAllMeterReadings();
      final topUps = await _topUpRepository.getAllTopUps();

      if (meterReadings.length < 2) {
        return 0.0;
      }

      final sortedReadings = List.from(meterReadings)
        ..sort((a, b) => a.date.compareTo(b.date));
      final firstReading = sortedReadings.first;
      final lastReading = sortedReadings.last;
      final totalDays = lastReading.date.difference(firstReading.date).inDays;

      if (totalDays > 0) {
        double totalTopUps = 0;
        for (var topUp in topUps) {
          if (topUp.date.isAfter(firstReading.date) &&
              topUp.date.isBefore(lastReading.date)) {
            totalTopUps += topUp.amount;
          }
        }
        final totalUsage = firstReading.value - lastReading.value + totalTopUps;
        if (totalUsage > 0) {
          return totalUsage / totalDays;
        }
      }
      return 0.0;
    } catch (e) {
      Logger.error('CostRepository: Fallback calculation failed: $e');
      return 0.0;
    }
  }

  /// Get first meter reading date
  Future<DateTime?> getFirstMeterReadingDate() async {
    try {
      final meterReadings = await _meterReadingRepository.getAllMeterReadings();
      return EntryFilterUtils.getFirstValidMeterReadingDate(meterReadings);
    } catch (e) {
      Logger.error('Failed to get first meter reading date: $e');
      return null;
    }
  }

  /// Get last meter reading date
  Future<DateTime?> getLastMeterReadingDate() async {
    try {
      final meterReadings = await _meterReadingRepository.getAllMeterReadings();
      return EntryFilterUtils.getLastValidMeterReadingDate(meterReadings);
    } catch (e) {
      Logger.error('Failed to get last meter reading date: $e');
      return null;
    }
  }

  /// Get previous meter reading date (second-to-last chronologically)
  Future<DateTime?> getPreviousMeterReadingDate() async {
    try {
      final meterReadings = await _meterReadingRepository.getAllMeterReadings();
      return EntryFilterUtils.getPreviousValidMeterReadingDate(meterReadings);
    } catch (e) {
      Logger.error('Failed to get previous meter reading date: $e');
      return null;
    }
  }

  /// Check if there are sufficient meter readings (at least 2)
  Future<bool> hasSufficientMeterReadings() async {
    try {
      final meterReadings = await _meterReadingRepository.getAllMeterReadings();
      return meterReadings.length >= 2;
    } catch (e) {
      Logger.error('Failed to check sufficient meter readings: $e');
      return false;
    }
  }

  /// Get recent average chart data for visualization
  Future<List<ChartData>> getRecentAverageChartData(
      DateTime? fromDate, DateTime? toDate) async {
    try {
      List<dynamic> averages;
      if (fromDate != null && toDate != null) {
        averages = await _perReadingAverageRepository
            .getPerReadingAveragesForDateRange(fromDate, toDate);
      } else {
        averages =
            await _perReadingAverageRepository.getAllPerReadingAverages();
      }

      final chartData = averages
          .map((avg) => ChartData(
                date: avg.readingDate,
                usage: avg.recentAveragePerDay,
                cost: avg.recentAveragePerDay,
              ))
          .toList();

      // Mark gap points for line breaks
      return _markGapPoints(chartData);
    } catch (e) {
      Logger.error('Failed to get recent average chart data: $e');
      return [];
    }
  }

  /// Get recent average chart data for all averages (last 100)
  Future<List<ChartData>> getRecentAverageChartDataForAllAverages() async {
    try {
      final averages =
          await _perReadingAverageRepository.getRecentPerReadingAverages(100);
      final chartData = averages
          .map((avg) => ChartData(
                date: avg.readingDate,
                usage: avg.recentAveragePerDay,
                cost: avg.recentAveragePerDay,
              ))
          .toList();

      // Mark gap points for line breaks
      return _markGapPoints(chartData);
    } catch (e) {
      Logger.error(
          'Failed to get recent average chart data for all averages: $e');
      return [];
    }
  }

  /// Mark gap points in chart data for line breaks
  List<ChartData> _markGapPoints(List<ChartData> chartData) {
    if (chartData.length < 2) return chartData;

    final markedData = <ChartData>[];

    for (int i = 0; i < chartData.length; i++) {
      bool isGapStart = false;
      bool isGapEnd = false;

      // Check if this is the start of a gap (>62 days to next point)
      if (i < chartData.length - 1) {
        final daysDiff =
            chartData[i + 1].date.difference(chartData[i].date).inDays;
        if (daysDiff > 62) {
          isGapStart = true;
        }
      }

      // Check if this is the end of a gap (>62 days from previous point)
      if (i > 0) {
        final daysDiff =
            chartData[i].date.difference(chartData[i - 1].date).inDays;
        if (daysDiff > 62) {
          isGapEnd = true;
        }
      }

      markedData.add(chartData[i].copyWith(
        isGapStart: isGapStart,
        isGapEnd: isGapEnd,
      ));
    }

    return markedData;
  }
}
