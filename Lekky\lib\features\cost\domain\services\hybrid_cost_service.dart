import '../../../../core/utils/date_time_utils.dart';
import '../../../../core/utils/logger.dart';
import '../../../meter_readings/domain/repositories/meter_reading_repository.dart';
import '../../../top_ups/domain/repositories/top_up_repository.dart';
import '../../../averages/domain/repositories/per_reading_average_repository.dart';
import '../models/cost_result.dart';

class HybridCostService {
  final MeterReadingRepository _meterReadingRepository;
  final TopUpRepository _topUpRepository;
  final PerReadingAverageRepository _perReadingAverageRepository;

  const HybridCostService(
    this._meterReadingRepository,
    this._topUpRepository,
    this._perReadingAverageRepository,
  );

  /// Calculate custom period cost and return both cost and calculation method
  Future<Map<String, dynamic>> calculateCustomPeriodCostWithMethod(
      DateTime fromDate, DateTime toDate) async {
    final allReadings = await _meterReadingRepository.getAllMeterReadings();
    final allTopUps = await _topUpRepository.getAllTopUps();

    Logger.info(
        'HybridCostService: Calculating custom period cost with method tracking from ${fromDate.toIso8601String()} to ${toDate.toIso8601String()}');

    if (allReadings.isEmpty) {
      Logger.warning('HybridCostService: No meter readings available');
      return {
        'cost': 0.0,
        'method': CalculationMethod.historicTotalAverage,
      };
    }

    allReadings.sort((a, b) => a.date.compareTo(b.date));

    // Find readings within the period (inclusive of boundaries)
    final readingsInPeriod = allReadings
        .where((reading) =>
            (reading.date.isAfter(fromDate) ||
                reading.date.isAtSameMomentAs(fromDate)) &&
            (reading.date.isBefore(toDate) ||
                reading.date.isAtSameMomentAs(toDate)))
        .toList();

    Logger.info(
        'HybridCostService: Found ${readingsInPeriod.length} readings within the period');

    if (readingsInPeriod.length >= 2) {
      // We have multiple readings in the period - use actual historic data
      Logger.info(
          'HybridCostService: Using actual historic data calculation with ${readingsInPeriod.length} readings');
      final cost = await _calculateActualHistoricCost(
          fromDate, toDate, readingsInPeriod, allTopUps);
      return {
        'cost': cost,
        'method': CalculationMethod.historicData,
      };
    } else {
      // Use per-reading recent averages for historic accuracy
      Logger.info(
          'HybridCostService: Using historic recent averages calculation');
      final cost = await _calculateUsingHistoricRecentAverages(
          fromDate, toDate, readingsInPeriod, allReadings);
      return {
        'cost': cost,
        'method': CalculationMethod.historicRecentAverage,
      };
    }
  }

  /// Legacy method for backward compatibility
  Future<double> calculateCustomPeriodCost(
      DateTime fromDate, DateTime toDate) async {
    final result = await calculateCustomPeriodCostWithMethod(fromDate, toDate);
    return result['cost'] as double;
  }

  /// Calculate cost using actual historic data when multiple readings are available
  Future<double> _calculateActualHistoricCost(DateTime fromDate,
      DateTime toDate, List readingsInPeriod, List allTopUps) async {
    double totalCost = 0.0;

    Logger.info(
        'HybridCostService: Using actual historic data calculation with ${readingsInPeriod.length} readings');

    // Sort readings by date
    readingsInPeriod.sort((a, b) => a.date.compareTo(b.date));

    // Calculate actual usage between consecutive readings
    for (int i = 0; i < readingsInPeriod.length - 1; i++) {
      final currentReading = readingsInPeriod[i];
      final nextReading = readingsInPeriod[i + 1];

      // Find top-ups between these readings
      double topUpsBetween = 0.0;
      for (final topUp in allTopUps) {
        if (topUp.date.isAfter(currentReading.date) &&
            topUp.date.isBefore(nextReading.date)) {
          topUpsBetween += topUp.amount;
        }
      }

      // Calculate actual usage: previous_reading - current_reading + top_ups
      final usage = (currentReading.value - nextReading.value) + topUpsBetween;
      if (usage > 0) {
        totalCost += usage;
      }
    }

    Logger.info(
        'HybridCostService: Actual historic cost calculated: $totalCost');
    return totalCost;
  }

  /// Calculate cost using historic recent averages for maximum accuracy
  Future<double> _calculateUsingHistoricRecentAverages(DateTime fromDate,
      DateTime toDate, List readingsInPeriod, List allReadings) async {
    double totalCost = 0.0;

    Logger.info(
        'HybridCostService: Using historic recent averages calculation');

    if (readingsInPeriod.isNotEmpty) {
      // We have one reading in the period - use its recent average
      final reading = readingsInPeriod.first;

      // Get per-reading recent average for this reading
      final average = await _perReadingAverageRepository
          .getPerReadingAverageByMeterReadingId(reading.id!);

      if (average != null && average.recentAveragePerDay > 0) {
        final days = DateTimeUtils.calculateDaysWithPrecision(fromDate, toDate);
        totalCost = average.recentAveragePerDay * days;
        Logger.info(
            'HybridCostService: Used per-reading recent average ${average.recentAveragePerDay} for $days days');
      } else {
        // Fallback to calculating recent average from nearby readings
        totalCost = await _calculateRecentAverageFromNearbyReadings(
            fromDate, toDate, reading, allReadings);
      }
    } else {
      // No readings in period - find the best recent average to use
      totalCost = await _calculateUsingBestAvailableRecentAverage(
          fromDate, toDate, allReadings);
    }

    return totalCost;
  }

  /// Calculate recent average from nearby readings when stored average unavailable
  Future<double> _calculateRecentAverageFromNearbyReadings(DateTime fromDate,
      DateTime toDate, dynamic centerReading, List allReadings) async {
    Logger.info(
        'HybridCostService: Calculating recent average from nearby readings');

    // Find readings before and after the center reading
    final centerDate = centerReading.date as DateTime;
    final readingsBeforeCenter = allReadings
        .where((reading) => reading.date.isBefore(centerDate))
        .toList();
    final readingsAfterCenter = allReadings
        .where((reading) => reading.date.isAfter(centerDate))
        .toList();

    // Sort to get closest readings
    readingsBeforeCenter
        .sort((a, b) => b.date.compareTo(a.date)); // Newest first
    readingsAfterCenter
        .sort((a, b) => a.date.compareTo(b.date)); // Oldest first

    // Try to find a recent reading pair for calculation
    if (readingsBeforeCenter.isNotEmpty) {
      final prevReading = readingsBeforeCenter.first;
      final days = DateTimeUtils.calculateDaysWithPrecision(
          prevReading.date, centerReading.date);

      if (days > 0 && days <= 62) {
        // Calculate recent average from this pair
        final allTopUps = await _topUpRepository.getAllTopUps();
        double topUpsBetween = 0.0;

        for (final topUp in allTopUps) {
          if (topUp.date.isAfter(prevReading.date) &&
              topUp.date.isBefore(centerReading.date)) {
            topUpsBetween += topUp.amount;
          }
        }

        final usage = (prevReading.value - centerReading.value) + topUpsBetween;
        if (usage > 0) {
          final recentAverage = usage / days;
          final requestedDays =
              DateTimeUtils.calculateDaysWithPrecision(fromDate, toDate);
          Logger.info(
              'HybridCostService: Calculated recent average $recentAverage from nearby readings for $requestedDays days');
          return recentAverage * requestedDays;
        }
      }
    }

    // Fallback to on-demand calculation
    Logger.info(
        'HybridCostService: Falling back to on-demand calculation for nearby readings');
    return await _calculateUsingOnDemandAverages(fromDate, toDate);
  }

  /// Calculate using the best available recent average
  Future<double> _calculateUsingBestAvailableRecentAverage(
      DateTime fromDate, DateTime toDate, List allReadings) async {
    Logger.info(
        'HybridCostService: Finding best available recent average for period');

    // Find the closest reading to the period
    dynamic closestReading;
    Duration minDistance =
        const Duration(days: 365 * 10); // Large initial value

    for (final reading in allReadings) {
      final distanceToStart = (reading.date.difference(fromDate)).abs();
      final distanceToEnd = (reading.date.difference(toDate)).abs();
      final minDistanceToReading =
          distanceToStart < distanceToEnd ? distanceToStart : distanceToEnd;

      if (minDistanceToReading < minDistance) {
        minDistance = minDistanceToReading;
        closestReading = reading;
      }
    }

    if (closestReading != null) {
      // Get per-reading recent average for the closest reading
      final average = await _perReadingAverageRepository
          .getPerReadingAverageByMeterReadingId(closestReading.id!);

      if (average != null && average.recentAveragePerDay > 0) {
        final days = DateTimeUtils.calculateDaysWithPrecision(fromDate, toDate);
        Logger.info(
            'HybridCostService: Used closest reading recent average ${average.recentAveragePerDay} for $days days');
        return average.recentAveragePerDay * days;
      }
    }

    // Final fallback to on-demand calculation
    Logger.info(
        'HybridCostService: No suitable recent averages found, using on-demand calculation');
    return await _calculateUsingOnDemandAverages(fromDate, toDate);
  }

  /// Calculate cost using on-demand averages when no stored averages available
  Future<double> _calculateUsingOnDemandAverages(
      DateTime fromDate, DateTime toDate) async {
    Logger.info('HybridCostService: Calculating on-demand averages');

    // Get all data for calculation
    final allReadings = await _meterReadingRepository.getAllMeterReadings();
    final allTopUps = await _topUpRepository.getAllTopUps();

    if (allReadings.isEmpty) {
      Logger.info(
          'HybridCostService: No readings available for on-demand calculation');
      return 0.0;
    }

    // Calculate total average from all available data
    final allEntries = [
      ...allReadings.map((reading) => ({
            'date': reading.date,
            'reading': reading.value,
            'topUp': 0.0,
          })),
      ...allTopUps.map((topUp) => ({
            'date': topUp.date,
            'reading': 0.0,
            'topUp': topUp.amount,
          })),
    ];

    allEntries.sort(
        (a, b) => (a['date'] as DateTime).compareTo(b['date'] as DateTime));

    if (allEntries.length < 2) {
      Logger.info(
          'HybridCostService: Insufficient data for on-demand calculation');
      return 0.0;
    }

    // Calculate total usage and time span
    double totalUsage = 0.0;
    double totalTopUps = 0.0;

    for (final entry in allEntries) {
      if (entry['topUp'] as double > 0) {
        totalTopUps += entry['topUp'] as double;
      }
    }

    final firstReading = allEntries.first['reading'] as double;
    final lastReading = allEntries.last['reading'] as double;
    totalUsage = (firstReading - lastReading) + totalTopUps;

    final totalDays = (allEntries.last['date'] as DateTime)
        .difference(allEntries.first['date'] as DateTime)
        .inDays;

    if (totalDays <= 0 || totalUsage <= 0) {
      Logger.info('HybridCostService: Invalid data for on-demand calculation');
      return 0.0;
    }

    final averagePerDay = totalUsage / totalDays;
    final requestedDays =
        DateTimeUtils.calculateDaysWithPrecision(fromDate, toDate);

    Logger.info(
        'HybridCostService: Calculated on-demand average: $averagePerDay per day for $requestedDays days');
    return averagePerDay * requestedDays;
  }
}
