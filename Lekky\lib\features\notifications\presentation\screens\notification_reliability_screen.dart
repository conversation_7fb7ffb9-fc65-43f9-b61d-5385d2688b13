import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../../../core/services/android_notification_manager.dart';
import '../../../../core/services/ios_notification_manager.dart';
import '../../../../core/services/background_execution_manager.dart';
import '../../../../core/services/battery_optimization_manager.dart';
import '../../../../core/services/notification_permission_manager.dart';
import '../../../../core/utils/logger.dart';
import '../../../../core/widgets/app_banner.dart';
import '../../../../core/theme/app_colors.dart';
import '../widgets/permission_status_widget.dart';

/// Screen for displaying notification system reliability status
class NotificationReliabilityScreen extends ConsumerStatefulWidget {
  const NotificationReliabilityScreen({super.key});

  @override
  ConsumerState<NotificationReliabilityScreen> createState() =>
      _NotificationReliabilityScreenState();
}

class _NotificationReliabilityScreenState
    extends ConsumerState<NotificationReliabilityScreen> {
  bool _isLoading = true;
  Map<String, dynamic> _reliabilityStatus = {};

  @override
  void initState() {
    super.initState();
    _loadReliabilityStatus();
  }

  Future<void> _loadReliabilityStatus() async {
    setState(() => _isLoading = true);

    try {
      final status = <String, dynamic>{};

      // Get platform-specific status
      if (Platform.isAndroid) {
        final androidManager = AndroidNotificationManager();
        status['android'] = await androidManager.getAndroidNotificationStatus();

        final batteryManager = BatteryOptimizationManager();
        status['battery'] = await batteryManager.getBatteryOptimizationStatus();
      } else if (Platform.isIOS) {
        final iosManager = IOSNotificationManager();
        status['ios'] = await iosManager.getIOSNotificationStatus();
      }

      // Get background execution status
      final backgroundManager = BackgroundExecutionManager();
      status['background'] =
          await backgroundManager.getBackgroundExecutionStatus();

      // Get permission status
      final permissionManager = NotificationPermissionManager();
      status['permissions'] = {
        'hasPermission': await permissionManager.hasPermission(),
        'timestamp': DateTime.now().toIso8601String(),
      };

      setState(() {
        _reliabilityStatus = status;
        _isLoading = false;
      });
    } catch (e) {
      Logger.error('Error loading reliability status: $e');
      setState(() => _isLoading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvoked: (didPop) async {
        if (didPop) return;
        context.go('/main-settings/notification-utilities');
      },
      child: Scaffold(
        body: Column(
          children: [
            // Banner with back arrow and theme-aware colors (same as Reminders screen)
            GestureDetector(
              onTap: () => context.go('/main-settings/notification-utilities'),
              child: AppBanner(
                message: '← Notification Reliability',
                gradientColors: AppColors.getSettingsMainCardGradient(
                    Theme.of(context).brightness == Brightness.dark),
                textColor: AppColors.getAppBarTextColor('settings',
                    Theme.of(context).brightness == Brightness.dark),
              ),
            ),
            Expanded(
              child: _isLoading
                  ? const Center(child: CircularProgressIndicator())
                  : RefreshIndicator(
                      onRefresh: _loadReliabilityStatus,
                      child: SingleChildScrollView(
                        physics: const AlwaysScrollableScrollPhysics(),
                        padding: const EdgeInsets.all(16.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            _buildOverallStatus(),
                            const SizedBox(height: 24),
                            _buildPermissionStatus(),
                            const SizedBox(height: 24),
                            _buildPlatformSpecificStatus(),
                            const SizedBox(height: 24),
                            _buildBackgroundExecutionStatus(),
                            const SizedBox(height: 24),
                            _buildActionButtons(),
                            const SizedBox(height: 24),
                            _buildTroubleshootingSection(),
                          ],
                        ),
                      ),
                    ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildOverallStatus() {
    final isReliable = _calculateOverallReliability();

    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  isReliable ? Icons.check_circle : Icons.warning,
                  color: isReliable ? Colors.green : Colors.orange,
                  size: 32,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Notification System',
                        style: Theme.of(context).textTheme.titleLarge,
                      ),
                      Text(
                        isReliable ? 'Reliable' : 'Needs Attention',
                        style: TextStyle(
                          color: isReliable ? Colors.green : Colors.orange,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Text(
              isReliable
                  ? 'Your notification system is properly configured for reliable delivery.'
                  : 'Some settings need attention to ensure reliable notification delivery.',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPermissionStatus() {
    final permissionData =
        _reliabilityStatus['permissions'] as Map<String, dynamic>?;
    final hasPermission = permissionData?['hasPermission'] as bool? ?? false;

    return PermissionStatusWidget(
      title: 'Notification Permissions',
      hasPermission: hasPermission,
      onRequestPermission: () async {
        final permissionManager = NotificationPermissionManager();
        await permissionManager.requestPermission(context);
        await _loadReliabilityStatus();
      },
    );
  }

  Widget _buildPlatformSpecificStatus() {
    if (Platform.isAndroid) {
      return _buildAndroidStatus();
    } else if (Platform.isIOS) {
      return _buildIOSStatus();
    } else {
      return const SizedBox.shrink();
    }
  }

  Widget _buildAndroidStatus() {
    final androidData = _reliabilityStatus['android'] as Map<String, dynamic>?;
    final batteryData = _reliabilityStatus['battery'] as Map<String, dynamic>?;

    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Android Settings',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 12),
            _buildStatusItem(
              'Notification Permission',
              androidData?['notificationPermission']
                      ?.toString()
                      .contains('granted') ??
                  false,
            ),
            _buildStatusItem(
              'Exact Alarm Permission',
              androidData?['exactAlarmPermission']
                      ?.toString()
                      .contains('granted') ??
                  false,
            ),
            _buildStatusItem(
              'Battery Optimization Exemption',
              !(batteryData?['isBatteryOptimized'] as bool? ?? true),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildIOSStatus() {
    final iosData = _reliabilityStatus['ios'] as Map<String, dynamic>?;

    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'iOS Settings',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 12),
            _buildStatusItem(
              'Notification Permissions',
              iosData?['permissionsGranted'] as bool? ?? false,
            ),
            _buildStatusItem(
              'Background App Refresh',
              true, // Cannot be checked programmatically
              subtitle: 'Enable in Settings > General > Background App Refresh',
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBackgroundExecutionStatus() {
    final backgroundData =
        _reliabilityStatus['background'] as Map<String, dynamic>?;
    final isEnabled =
        backgroundData?['isBackgroundExecutionEnabled'] as bool? ?? false;

    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Background Execution',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 12),
            _buildStatusItem(
              'Background Execution Enabled',
              isEnabled,
              subtitle: isEnabled
                  ? 'App can run in background for notifications'
                  : 'Background execution may be limited',
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusItem(String title, bool isEnabled, {String? subtitle}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        children: [
          Icon(
            isEnabled ? Icons.check_circle : Icons.cancel,
            color: isEnabled ? Colors.green : Colors.red,
            size: 20,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(title),
                if (subtitle != null)
                  Text(
                    subtitle,
                    style: Theme.of(context).textTheme.bodySmall,
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        ElevatedButton.icon(
          onPressed: _testNotification,
          icon: const Icon(Icons.notifications_active),
          label: const Text('Test Notification'),
        ),
        const SizedBox(height: 8),
        OutlinedButton.icon(
          onPressed: _showTroubleshootingGuide,
          icon: const Icon(Icons.help_outline),
          label: const Text('Troubleshooting Guide'),
        ),
      ],
    );
  }

  Widget _buildTroubleshootingSection() {
    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Quick Tips',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 12),
            const Text(
                '• Restart the app after changing notification settings'),
            const Text('• Test with a short reminder interval first'),
            const Text('• Check your device\'s Do Not Disturb settings'),
            const Text('• Ensure the app has all required permissions'),
          ],
        ),
      ),
    );
  }

  bool _calculateOverallReliability() {
    final permissionData =
        _reliabilityStatus['permissions'] as Map<String, dynamic>?;
    final hasPermission = permissionData?['hasPermission'] as bool? ?? false;

    final backgroundData =
        _reliabilityStatus['background'] as Map<String, dynamic>?;
    final backgroundEnabled =
        backgroundData?['isBackgroundExecutionEnabled'] as bool? ?? false;

    return hasPermission && backgroundEnabled;
  }

  Future<void> _testNotification() async {
    // Implementation for testing notification
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Test notification sent! Check your notification panel.'),
      ),
    );
  }

  Future<void> _showTroubleshootingGuide() async {
    final backgroundManager = BackgroundExecutionManager();
    await backgroundManager.showTroubleshootingGuide(context);
  }
}
