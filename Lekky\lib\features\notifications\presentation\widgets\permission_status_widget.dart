import 'package:flutter/material.dart';

/// Widget for displaying permission status with action button
class PermissionStatusWidget extends StatelessWidget {
  final String title;
  final bool hasPermission;
  final VoidCallback? onRequestPermission;
  final String? description;

  const PermissionStatusWidget({
    super.key,
    required this.title,
    required this.hasPermission,
    this.onRequestPermission,
    this.description,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  hasPermission ? Icons.check_circle : Icons.error,
                  color: hasPermission ? Colors.green : Colors.red,
                  size: 24,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    title,
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              hasPermission
                  ? 'Permission granted'
                  : 'Permission required for notifications',
              style: TextStyle(
                color: hasPermission ? Colors.green : Colors.red,
                fontWeight: FontWeight.w500,
              ),
            ),
            if (description != null) ...[
              const SizedBox(height: 8),
              Text(
                description!,
                style: Theme.of(context).textTheme.bodySmall,
              ),
            ],
            if (!hasPermission && onRequestPermission != null) ...[
              const SizedBox(height: 12),
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: onRequestPermission,
                  child: const Text('Grant Permission'),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
}

/// Widget for displaying multiple permission statuses
class PermissionStatusList extends StatelessWidget {
  final List<PermissionStatusItem> permissions;

  const PermissionStatusList({
    super.key,
    required this.permissions,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: permissions
          .map((permission) => Padding(
                padding: const EdgeInsets.only(bottom: 8.0),
                child: PermissionStatusWidget(
                  title: permission.title,
                  hasPermission: permission.hasPermission,
                  onRequestPermission: permission.onRequestPermission,
                  description: permission.description,
                ),
              ))
          .toList(),
    );
  }
}

/// Data class for permission status items
class PermissionStatusItem {
  final String title;
  final bool hasPermission;
  final VoidCallback? onRequestPermission;
  final String? description;

  const PermissionStatusItem({
    required this.title,
    required this.hasPermission,
    this.onRequestPermission,
    this.description,
  });
}

/// Widget for displaying permission status with detailed breakdown
class DetailedPermissionStatusWidget extends StatelessWidget {
  final String title;
  final List<PermissionDetail> details;
  final VoidCallback? onRequestAllPermissions;

  const DetailedPermissionStatusWidget({
    super.key,
    required this.title,
    required this.details,
    this.onRequestAllPermissions,
  });

  @override
  Widget build(BuildContext context) {
    final allGranted = details.every((detail) => detail.isGranted);

    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  allGranted ? Icons.check_circle : Icons.warning,
                  color: allGranted ? Colors.green : Colors.orange,
                  size: 24,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    title,
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            ...details.map((detail) => _buildPermissionDetail(context, detail)),
            if (!allGranted && onRequestAllPermissions != null) ...[
              const SizedBox(height: 12),
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: onRequestAllPermissions,
                  child: const Text('Request All Permissions'),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildPermissionDetail(BuildContext context, PermissionDetail detail) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        children: [
          Icon(
            detail.isGranted ? Icons.check : Icons.close,
            color: detail.isGranted ? Colors.green : Colors.red,
            size: 16,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  detail.name,
                  style: Theme.of(context).textTheme.bodyMedium,
                ),
                if (detail.description != null)
                  Text(
                    detail.description!,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Colors.grey[600],
                        ),
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

/// Data class for permission details
class PermissionDetail {
  final String name;
  final bool isGranted;
  final String? description;

  const PermissionDetail({
    required this.name,
    required this.isGranted,
    this.description,
  });
}

/// Widget for displaying permission status with progress indicator
class PermissionProgressWidget extends StatelessWidget {
  final String title;
  final int grantedCount;
  final int totalCount;
  final List<String> grantedPermissions;
  final List<String> missingPermissions;

  const PermissionProgressWidget({
    super.key,
    required this.title,
    required this.grantedCount,
    required this.totalCount,
    required this.grantedPermissions,
    required this.missingPermissions,
  });

  @override
  Widget build(BuildContext context) {
    final progress = totalCount > 0 ? grantedCount / totalCount : 0.0;
    final isComplete = grantedCount == totalCount;

    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  isComplete ? Icons.check_circle : Icons.hourglass_empty,
                  color: isComplete ? Colors.green : Colors.orange,
                  size: 24,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    title,
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                ),
                Text(
                  '$grantedCount/$totalCount',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            LinearProgressIndicator(
              value: progress,
              backgroundColor: Colors.grey[300],
              valueColor: AlwaysStoppedAnimation<Color>(
                isComplete ? Colors.green : Colors.orange,
              ),
            ),
            const SizedBox(height: 12),
            if (grantedPermissions.isNotEmpty) ...[
              Text(
                'Granted:',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: Colors.green,
                    ),
              ),
              ...grantedPermissions.map((permission) => Text(
                    '• $permission',
                    style: Theme.of(context).textTheme.bodySmall,
                  )),
            ],
            if (missingPermissions.isNotEmpty) ...[
              const SizedBox(height: 8),
              Text(
                'Missing:',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: Colors.red,
                    ),
              ),
              ...missingPermissions.map((permission) => Text(
                    '• $permission',
                    style: Theme.of(context).textTheme.bodySmall,
                  )),
            ],
          ],
        ),
      ),
    );
  }
}
