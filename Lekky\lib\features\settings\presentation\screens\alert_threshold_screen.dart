import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/providers/settings_provider.dart';
import '../../../../core/providers/settings_navigation_provider.dart';
import '../../../../core/shared/widgets/settings_section_header.dart';
import '../../../../core/shared/widgets/currency_input_field.dart';
import '../widgets/base_settings_screen.dart';

/// Alert Threshold settings screen
class AlertThresholdScreen extends BaseSettingsScreen {
  /// Constructor
  const AlertThresholdScreen({super.key})
      : super(
          title: 'Alert Threshold',
          categoryIndex: SettingsCategoryIndex.notifications,
        );

  @override
  Widget buildContent(BuildContext context, WidgetRef ref) {
    return ref.watch(settingsProvider).when(
          loading: () => const Center(child: CircularProgressIndicator()),
          error: (error, stack) => Center(child: Text('Error: $error')),
          data: (settings) {
            return SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Alert settings card
                  Card(
                    margin: const EdgeInsets.all(8.0),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12.0),
                    ),
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const SettingsSectionHeader(
                            title: 'Alert Threshold',
                            description:
                                'Set the balance level that triggers low balance alerts.',
                            icon: Icons.warning,
                          ),
                          const SizedBox(height: 16),
                          const Text(
                            'You will receive alerts when your balance falls below this amount.',
                            style: TextStyle(fontSize: 14),
                          ),
                          const SizedBox(height: 8),
                          CurrencyInputField(
                            value: settings.alertThreshold,
                            onChanged: (value) {
                              if (value != null) {
                                ref
                                    .read(settingsProvider.notifier)
                                    .updateAlertThreshold(value);
                                showSuccessMessage(
                                    context, 'Alert threshold updated');
                              }
                            },
                            currencySymbol: settings.currencySymbol,
                            labelText: 'Alert Threshold',
                            hintText: 'Enter amount',
                            minValue: 1.00,
                            maxValue: 999.99,
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            );
          },
        );
  }
}
