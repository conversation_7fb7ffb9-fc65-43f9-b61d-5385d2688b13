import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../../../../core/services/data_deletion_service.dart';
import '../dialogs/delete_all_data_dialog.dart';
import '../../../../core/theme/app_colors.dart';
import '../../../../core/widgets/app_banner.dart';
import '../../../../core/providers/settings_navigation_provider.dart';

/// Screen for deleting all data with confirmation
class DeleteAllDataScreen extends StatefulWidget {
  /// Constructor
  const DeleteAllDataScreen({super.key});

  @override
  State<DeleteAllDataScreen> createState() => _DeleteAllDataScreenState();
}

class _DeleteAllDataScreenState extends State<DeleteAllDataScreen> {
  bool _isDeleting = false;
  bool _deletionCompleted = false;
  bool _deletionSuccessful = false;
  DataCounts? _dataCounts;
  double _progress = 0.0;

  @override
  void initState() {
    super.initState();
    _loadDataCounts();
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: !_isDeleting,
      onPopInvoked: (didPop) async {
        if (didPop) return;
        context
            .go('/main-settings?expanded=${SettingsCategoryIndex.dataBackup}');
      },
      child: Scaffold(
        body: Column(
          children: [
            // Banner with back arrow
            GestureDetector(
              onTap: () => context.go(
                  '/main-settings?expanded=${SettingsCategoryIndex.dataBackup}'),
              child: AppBanner(
                message: '← Delete All Data',
                gradientColors: AppColors.getSettingsMainCardGradient(
                    Theme.of(context).brightness == Brightness.dark),
                textColor: AppColors.getAppBarTextColor('settings',
                    Theme.of(context).brightness == Brightness.dark),
              ),
            ),
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Information Card
                    Card(
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Icon(Icons.delete_forever,
                                    color: Colors.red, size: 24),
                                const SizedBox(width: 16),
                                const Text(
                                  'Delete All Data',
                                  style: TextStyle(
                                    fontSize: 18,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 16),
                            const Text(
                              'This will permanently delete all your meter readings and top-ups from the database. This action cannot be undone.',
                              style: TextStyle(fontSize: 14),
                            ),
                            const SizedBox(height: 16),
                            if (_dataCounts != null) ...[
                              _buildDataCountsDisplay(),
                              const SizedBox(height: 16),
                            ],
                            Container(
                              padding: const EdgeInsets.all(12),
                              decoration: BoxDecoration(
                                color: Colors.red.withOpacity(0.1),
                                borderRadius: BorderRadius.circular(8),
                                border: Border.all(
                                    color: Colors.red.withOpacity(0.3)),
                              ),
                              child: Row(
                                children: [
                                  Icon(Icons.warning,
                                      color: Colors.red, size: 20),
                                  const SizedBox(width: 8),
                                  Expanded(
                                    child: Text(
                                      'Warning: This action is irreversible',
                                      style: TextStyle(
                                        fontSize: 14,
                                        fontWeight: FontWeight.bold,
                                        color: Colors.red.shade700,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),

                    const SizedBox(height: 24),

                    // Delete Button or Progress/Done Button
                    SizedBox(
                      width: double.infinity,
                      child: _buildActionButton(),
                    ),

                    if (_isDeleting) ...[
                      const SizedBox(height: 12),
                      // Progress bar with dynamic sizing
                      LayoutBuilder(
                        builder: (context, constraints) {
                          return Column(
                            children: [
                              SizedBox(
                                width: constraints.maxWidth,
                                child:
                                    LinearProgressIndicator(value: _progress),
                              ),
                              const SizedBox(height: 6),
                              SizedBox(
                                width: constraints.maxWidth,
                                child: Text(
                                  'Deleting data... ${(_progress * 100).toInt()}%',
                                  textAlign: TextAlign.center,
                                  style: TextStyle(
                                    fontSize:
                                        constraints.maxWidth < 300 ? 11 : 13,
                                  ),
                                  overflow: TextOverflow.ellipsis,
                                  maxLines: 1,
                                ),
                              ),
                            ],
                          );
                        },
                      ),
                    ],

                    const SizedBox(height: 12),

                    // Help Text
                    Text(
                      _deletionCompleted && _deletionSuccessful
                          ? 'All data has been successfully deleted from your device.'
                          : 'Make sure you have exported your data if you want to keep a backup before proceeding.',
                      style: const TextStyle(
                        fontSize: 12,
                        color: Colors.grey,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Build data counts display
  Widget _buildDataCountsDisplay() {
    if (_dataCounts == null) return const SizedBox.shrink();

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Current Data:',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text('Meter Readings:'),
              Text('${_dataCounts!.meterReadings}'),
            ],
          ),
          const SizedBox(height: 4),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text('Top-ups:'),
              Text('${_dataCounts!.topUps}'),
            ],
          ),
          const SizedBox(height: 8),
          const Divider(),
          const SizedBox(height: 4),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'Total Entries:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              Text(
                '${_dataCounts!.total}',
                style: const TextStyle(fontWeight: FontWeight.bold),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// Build action button based on current state
  Widget _buildActionButton() {
    if (_deletionCompleted) {
      return ElevatedButton(
        onPressed: () => _navigateBackToSettings(),
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.blue,
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(vertical: 16),
        ),
        child: const Text('Done'),
      );
    }

    if (_isDeleting) {
      return ElevatedButton(
        onPressed: null,
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.grey,
          padding: const EdgeInsets.symmetric(vertical: 16),
        ),
        child: const Text('Deleting...'),
      );
    }

    return ElevatedButton(
      onPressed:
          _dataCounts?.hasData == true ? () => _showDeleteConfirmation() : null,
      style: ElevatedButton.styleFrom(
        backgroundColor: Colors.red,
        foregroundColor: Colors.white,
        padding: const EdgeInsets.symmetric(vertical: 16),
      ),
      child: Text(
        _dataCounts?.hasData == true ? 'Delete All Data' : 'No Data to Delete',
      ),
    );
  }

  /// Load current data counts
  Future<void> _loadDataCounts() async {
    try {
      final service = DataDeletionService.create();
      final counts = await service.getDataCounts();

      if (mounted) {
        setState(() {
          _dataCounts = counts;
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to load data counts: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// Show delete confirmation dialog
  Future<void> _showDeleteConfirmation() async {
    if (_dataCounts == null) return;

    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => DeleteAllDataDialog(
        dataCounts: _dataCounts!,
      ),
    );

    if (confirmed == true && mounted) {
      await _performDeletion();
    }
  }

  /// Perform the data deletion
  Future<void> _performDeletion() async {
    setState(() {
      _isDeleting = true;
      _progress = 0.0;
    });

    try {
      final service = DataDeletionService.create();
      final success = await service.deleteAllData(
        onProgress: (progress) {
          if (mounted) {
            setState(() {
              _progress = progress;
            });
          }
        },
      );

      if (mounted) {
        setState(() {
          _isDeleting = false;
          _deletionCompleted = true;
          _deletionSuccessful = success;
        });

        if (success) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('All data deleted successfully'),
              backgroundColor: Colors.green,
              duration: Duration(seconds: 2),
            ),
          );
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Failed to delete data'),
              backgroundColor: Colors.red,
              duration: Duration(seconds: 3),
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isDeleting = false;
          _deletionCompleted = true;
          _deletionSuccessful = false;
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Deletion failed: $e'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    }
  }

  /// Navigate back to settings
  Future<void> _navigateBackToSettings() async {
    if (mounted) {
      context.go('/main-settings');
    }
  }
}
