import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:timezone/data/latest.dart' as tz;
import 'core/di/service_locator.dart';
import 'core/localization/app_localizations.dart';
import 'core/providers/riverpod_localization_provider.dart';
import 'core/providers/theme_provider.dart' as theme_provider;
import 'core/shared/models/theme_mode.dart';
import 'core/theme/app_theme.dart';
import 'core/routing/app_router.dart';
import 'features/reminders/presentation/widgets/reactive_reminder_listener.dart';
import 'features/notifications/presentation/widgets/reactive_alert_listener.dart';
import 'features/notifications/data/notification_service.dart';
import 'core/services/background_monitoring_service.dart';
import 'core/services/unified_alert_manager.dart';
import 'core/services/post_migration_validation_service.dart';
import 'core/services/status_field_migration_service.dart';
import 'core/constants/currency_constants.dart';
import 'package:logger/logger.dart'; // Added for logging

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  tz.initializeTimeZones();
  setupServiceLocator();

  final logger = Logger();

  try {
    await StatusFieldMigrationService.runStatusFieldSynchronization();
    logger.i('Status field synchronization completed');
  } catch (e, stackTrace) {
    logger.e(
        'Failed to run status field synchronization: $e\nStackTrace: $stackTrace');
  }

  try {
    await PostMigrationValidationService.checkAndRunPostMigrationValidation();
    logger.i('Post-migration validation completed');
  } catch (e, stackTrace) {
    logger.e(
        'Failed to run post-migration validation: $e\nStackTrace: $stackTrace');
  }

  try {
    final notificationService =
        await serviceLocator.getAsync<NotificationService>();
    final bool initialized = await notificationService.initialize();
    if (!initialized) {
      logger.w(
          'Notification service initialization failed - notifications will not work');
    } else {
      logger.i('Notification service initialized successfully');

      // Recover any missed notifications
      try {
        await notificationService.recoverMissedNotifications();
        logger.i('Notification recovery completed');
      } catch (e) {
        logger.w('Notification recovery failed: $e');
      }
    }
  } catch (e, stackTrace) {
    logger.e(
        'Failed to initialize notification service: $e\nStackTrace: $stackTrace');
  }

  try {
    final backgroundService = BackgroundMonitoringService();
    await backgroundService.initialize();
    await backgroundService.updateMonitoring();
    logger.i('Background monitoring service initialized');
  } catch (e, stackTrace) {
    logger.e(
        'Failed to initialize background monitoring: $e\nStackTrace: $stackTrace');
  }

  try {
    final alertManager = UnifiedAlertManager();
    await alertManager.checkAndFireAlerts();
    logger.i('Unified alert manager initialized');
  } catch (e, stackTrace) {
    logger.e('Failed to initialize alert manager: $e\nStackTrace: $stackTrace');
  }

  runApp(
    const ProviderScope(
      child: LekkyApp(),
    ),
  );
}

class LekkyApp extends ConsumerWidget {
  const LekkyApp({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final localizationAsync = ref.watch(riverpodLocalizationProvider);
    final themeAsync = ref.watch(theme_provider.themeProvider);
    final router = ref.watch(appRouterProvider);

    return localizationAsync.when(
      data: (localizationState) => ReactiveAlertListener(
        child: ReactiveNotificationListener(
          child: MaterialApp.router(
            title: 'Lekky',
            theme: AppTheme.lightTheme,
            darkTheme: AppTheme.darkTheme,
            themeMode: themeAsync.when(
              data: (themeState) => themeState.themeMode.toMaterialThemeMode(),
              loading: () => ThemeMode.system,
              error: (_, __) => ThemeMode.system,
            ),
            debugShowCheckedModeBanner: false,
            locale: Locale(localizationState.languageCode),
            supportedLocales: RegionalConstants.supportedLanguageCodes
                .map((code) => Locale(code))
                .toList(),
            localizationsDelegates: const [
              AppLocalizations.delegate,
              GlobalMaterialLocalizations.delegate,
              GlobalWidgetsLocalizations.delegate,
              GlobalCupertinoLocalizations.delegate,
            ],
            routerConfig: router,
          ),
        ),
      ),
      loading: () => const MaterialApp(
        home: Scaffold(
          body: Center(child: CircularProgressIndicator()),
        ),
      ),
      error: (error, stackTrace) => const MaterialApp(
        home: Scaffold(
          body: Center(
            child: Text('Error loading localization'),
          ),
        ),
      ),
    );
  }
}

// Extension to simplify theme mode conversion
extension ThemeModeExtension on AppThemeMode {
  ThemeMode toMaterialThemeMode() {
    switch (this) {
      case AppThemeMode.light:
        return ThemeMode.light;
      case AppThemeMode.dark:
        return ThemeMode.dark;
      case AppThemeMode.system:
        return ThemeMode.system;
    }
  }
}
